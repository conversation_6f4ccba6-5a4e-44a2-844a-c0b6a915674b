'==============================================================================
' 主函数：Dong_自动排版
' 功能：公文自动排版的主入口函数
' 作者：Dong
' 说明：按照公文格式标准自动排版Word文档
'==============================================================================
Sub Dong_自动排版()
'公文自动排版主程序
    Initial   '步骤1：初始化文档设置
    GWStyle  '步骤2：应用公文样式格式
    Title1   '步骤3：设置一级标题格式
    Inscribe '步骤4：处理落款和附件
    PageNumGW   '步骤5：设置公文页码
    Common '步骤6：公共部分调整和收尾工作
End Sub

'==============================================================================
' 函数：Initial
' 功能：初始化文档设置，为排版做准备
' 说明：设置页面参数、清理文档格式、处理表格等
'==============================================================================
Function Initial()
'文档初始化函数
    Dim t As Table  '定义表格变量，用于遍历文档中的表格

    '调用页面设置函数，设置为A4纸张大小和边距
    PaperSetup

    '设置页面显示模式为适合页面宽度，避免页面刷新问题
    ActiveWindow.ActivePane.View.Zoom.PageFit = wdPageFitBestFit

    '对当前活动文档进行初始化操作
    With ActiveDocument
        '从附加的模板中复制内置样式到当前文档，确保样式一致性
        .CopyStylesFromTemplate Template:=.AttachedTemplate.FullName

        '删除文档中的所有域（如日期、页码等自动更新字段），转为静态文本
        .Fields.Unlink

        '将文档中的自动编号列表转换为普通文本，便于后续处理
        .ConvertNumbersToText

        '使用查找替换功能清理文档格式
        With .Content.Find
            '将手动换行符(^l)替换为段落标记(^p)，统一换行格式
            .Execute "^l", , , 0, , , , , , "^p", 2
            '删除标点符号后的多余空格，保持格式整洁
            .Execute "([、.．）])([ 　^s^t]{1,})", , , 1, , , , , , "\1", 2
            '将“附”后面的内容标准化为“附件”格式
            .Execute "(^13附)([!一-﨩])", , , 1, , , , , , "\1件\2", 2
            '将“附表”统一替换为“附件”
            .Execute "附表", , , , , , , , , "附件", 2
        End With

        '处理文档中的表格格式
        For Each t In .Tables  '遍历文档中的所有表格
            With t.Range.Rows  '对表格的所有行进行设置
                .WrapAroundText = False  '取消文字环绕，表格不被文字环绕
                .Alignment = wdAlignRowCenter  '设置表格在页面中居中对齐
            End With
        Next
    End With
End Function

'==============================================================================
' 函数：PaperSetup
' 功能：设置文档的页面参数
' 说明：根据纸张方向设置不同的边距和页面尺寸
'==============================================================================
Function PaperSetup()
'页面设置函数
    Dim Sec As Section  '定义节变量，用于遍历文档的所有节

    '遍历文档中的所有节（一般文档只有一个节）
    For Each Sec In ActiveDocument.Sections
        With Sec.PageSetup  '设置当前节的页面参数
            '判断纸张方向：竖向（纵向）
            If .Orientation = wdOrientPortrait Then
                '竖向A4纸张设置（210mm x 297mm）
                .TopMargin = CentimetersToPoints(2.54)  '上边距：2.54厘米
                .BottomMargin = CentimetersToPoints(2.54)  '下边距：2.54厘米
                .LeftMargin = CentimetersToPoints(2.8)  '左边距：2.8厘米
                .RightMargin = CentimetersToPoints(2.6)  '右边距：2.6厘米
                .PageWidth = CentimetersToPoints(21)   '页面宽度：21厘米
                .PageHeight = CentimetersToPoints(29.7)  '页面高度：29.7厘米
            Else
                '横向纸张设置（297mm x 210mm）
                .TopMargin = CentimetersToPoints(2.5)  '上边距：2.5厘米
                .BottomMargin = CentimetersToPoints(2.5)  '下边距：2.5厘米
                .LeftMargin = CentimetersToPoints(2.54)  '左边距：2.54厘米
                .RightMargin = CentimetersToPoints(2.54)  '右边距：2.54厘米
                .PageWidth = CentimetersToPoints(29.7)  '页面宽度：29.7厘米
                .PageHeight = CentimetersToPoints(21)  '页面高度：21厘米
            End If
            '设置页脚距离页面底部的距离：1.75厘米
            .FooterDistance = CentimetersToPoints(1.75)
        End With
    Next
End Function

'==============================================================================
' 函数：GWStyle
' 功能：应用公文样式格式
' 说明：设置正文和各级标题的字体、段落格式，处理自动编号
'==============================================================================
Function GWStyle()
'公文样式设置函数
    '定义变量
    Dim doc As Document  '文档对象
    Dim i As Paragraph  '段落对象，用于遍历
    Dim r()  '范围数组，存储非表格区域
    Dim n As Long  '循环计数器
    Dim t2&, t3&, t4&, t5&  '各级标题的编号计数器

    '获取当前活动文档的引用
    Set doc = ActiveDocument

    '分离表格和文本区域，避免对表格内容进行样式处理
    With doc
        '根据表格数量初始化范围数组大小
        ReDim r(.Tables.Count + 1)

        '如果文档中没有表格
        If .Tables.Count = 0 Then
            '将整个文档内容作为一个范围
            Set r(1) = .Content
        Else
            '如果有表格，则分别获取表格之间的文本区域
            For n = 1 To .Tables.Count
                If n = 1 Then
                    '第一个区域：文档开始到第一个表格之前
                    Set r(n) = .Range(0, .Tables(n).Range.Start)
                Else
                    '中间区域：上一个表格结束到当前表格开始
                    Set r(n) = .Range(.Tables(n - 1).Range.End, .Tables(n).Range.Start)
                End If
            Next
            '最后一个区域：最后一个表格结束到文档结束
            Set r(n) = .Range(.Tables(n - 1).Range.End, .Content.End)
        End If
    End With

    '遍历所有非表格区域，对每个区域应用公文样式
    For n = 1 To UBound(r)
        With r(n)  '对当前区域进行处理
            .Select  '选中当前区域

            '执行删除段落首尾空格的命令（ID:122是Word内置命令）
            CommandBars.FindControl(ID:=122).Execute

            '清除当前选中区域的所有格式，重新开始设置
            Selection.ClearFormatting

            '设置正文的字体样式
            With .Font
                .NameFarEast = "方正仿宋_GBK"  '中文字体：方正仿宋
                .NameAscii = "宋体"  '英文字体：宋体
                .Size = 16  '字体大小：16磅（3号字体）
                .Color = wdColorBlue  '字体颜色：蓝色（临时标记，最后会恢复为黑色）
                .Kerning = 0  '字符间距：0
                .DisableCharacterSpaceGrid = True  '禁用字符网格
            End With
            '设置正文的段落格式
            With .ParagraphFormat
                .LineSpacingRule = wdLineSpaceExactly  '行距规则：固定值
                .LineSpacing = 29  '行距：29磅（公文标准）
                .CharacterUnitFirstLineIndent = 2  '首行缩进：2个字符
                .AutoAdjustRightIndent = False  '不自动调整右缩进
                .DisableLineHeightGrid = True  '禁用行高网格
            End With

            '如果不是文档开始位置，在前面插入一个段落
            If .Start <> 0 Then .InsertParagraphBefore

            With .Find
                .Execute "(^13)([一二三四五六七八九十百零〇○Oo0０Ｏｏ]@)(、)", , , 1, , , , , , "\1一\3", 2
                .Execute "(^13)([(（][一二三四五六七八九十百零〇○Oo0０Ｏｏ]@[）)])", , , 1, , , , , , "\1（一）", 2
                .Execute "(^13)([0-9０-９]@[、.．])", , , 1, , , , , , "\11．", 2
                .Execute "(^13)[(（][0-9０-９]@[）)]", , , 1, , , , , , "\1（1）", 2
            End With

            '标题格式/自动编号
            For Each i In .Paragraphs
                With i.Range
                    If .Text Like "一、*" Then
                        .Style = wdStyleHeading2
                        .Font.Color = wdColorRed
                        .Font.NameFarEast = "方正黑体_GBK"
                        .Font.NameAscii = "宋体"
                        t2 = t2 + 1
                        t3 = 0
                        t4 = 0
                        t5 = 0
                        doc.Range(Start:=.Start, End:=.Characters(InStr(.Text, "、")).Start).Select
                        Selection.Fields.Add Range:=Selection.Range, Text:="= " & t2 & " \* CHINESENUM3"

                    ElseIf .Text Like "（一）*" Then
                        .Style = wdStyleHeading3
                        .Font.Color = wdColorPink
                        .Font.NameFarEast = "方正仿宋_GBK"
                        .Font.NameAscii = "宋体"
                        t3 = t3 + 1
                        t4 = 0
                        t5 = 0
                        doc.Range(Start:=.Start + 1, End:=.Characters(InStr(.Text, "）")).Start).Select
                        Selection.Fields.Add Range:=Selection.Range, Text:="= " & t3 & " \* CHINESENUM3"

                    ElseIf .Text Like "#．*" Then
                        .Style = wdStyleHeading4
                        .Font.Color = wdColorGreen
                        .Font.NameFarEast = "方正仿宋_GBK"
                        .Font.NameAscii = "宋体"
                        With .Font
                            .Size = 16
                        End With
                        With .ParagraphFormat
                            .SpaceBefore = 13
                            .SpaceAfter = 13
                        End With
                        t4 = t4 + 1
                        t5 = 0
                        doc.Range(Start:=.Start, End:=.Characters(InStr(.Text, "．")).Start).Text = t4

                    ElseIf .Text Like "（#）*" Then
                        .Style = wdStyleHeading5
                        .Font.Color = wdColorOrange
                        .Font.NameFarEast = "方正仿宋_GBK"
                        .Font.NameAscii = "宋体"
                        With .Font
                            .Size = 16
                        End With
                        With .ParagraphFormat
                            .SpaceBefore = 13
                            .SpaceAfter = 13
                        End With
                        t5 = t5 + 1
                        doc.Range(Start:=.Characters(1).End, End:=.Characters(InStr(.Text, "）")).Start).Text = t5

                    ElseIf Asc(.Text) = 13 Then
                        .Delete

                    ElseIf .Text Like "[!^13]附件*" Or .Text Like "附件*" Then
                        t2 = 0
                        t3 = 0
                        t4 = 0
                        t5 = 0
                    End If

                    If .Style Like "标题*" Then
                        .Font.Kerning = 0
                        With .ParagraphFormat
                            .LineSpacingRule = wdLineSpaceExactly
                            .LineSpacing = 29  '固定值29磅
                            .CharacterUnitFirstLineIndent = 1.99
                            .AutoAdjustRightIndent = False
                            .DisableLineHeightGrid = True
                            .KeepWithNext = False
                            .KeepTogether = False
                        End With

                        If .Sentences(1) Like "*：??*" Then
                            .MoveStart 1, InStr(.Text, "：")
                            With .Font
                                .NameFarEast = "方正仿宋_GBK"
                                .NameAscii = "宋体"
                                .Bold = False
                                .Color = wdColorBlue
                            End With

                            If .Paragraphs(1).Range.Style Like "标题*" & "[23]" Then
                                If .Text Like "*[。：；，、！？…—.:;,!?]?" Then
                                    .Characters.Last.Previous.Delete
                                End If
                            ElseIf .Paragraphs(1).Range.Style Like "标题*" & "[45]" Then
                                If .Text Like "*[!。：；，、！？…—.:;,!?]?" Then
                                    If .Text Like "*[!0-9a-zA-Z]?" Then
                                        .Characters.Last.InsertBefore Text:="。"
                                    End If
                                End If
                            End If
                        Else
                            If .Sentences.Count = 1 Then
                                If .Text Like "*[。：；，、！？…—.:;,!?]?" Then .Characters.Last.Previous.Delete
                            Else
                                With doc.Range(Start:=.Sentences(1).End, End:=.End).Font
                                    .NameFarEast = "方正仿宋_GBK"
                                    .NameAscii = "宋体"
                                    .Bold = False
                                    .Color = wdColorBlue
                                End With
                            End If
                        End If
                    End If
                End With
            Next

            If .Start <> 0 Then
                If Len(.Text) <> 0 Then
                    .InsertParagraphBefore
                    With .Paragraphs(1).Range
                        .Font.Size = 6
                        With .ParagraphFormat
                            .SpaceBefore = 0
                            .SpaceAfter = 0
                        End With
                    End With
                End If
            End If
        End With
    Next

    '设置标题间距：除一级标题外，其他标题段前段后间距为0
    For Each i In doc.Paragraphs
        With i.Range
            On Error Resume Next
            Dim styleName As String
            styleName = .Style.NameLocal
            If (styleName = "标题2" Or styleName = "标题3" Or styleName = "标题4" Or styleName = "标题5") Then
                With .ParagraphFormat
                    .SpaceBefore = 0
                    .SpaceAfter = 0
                End With
            End If
            On Error GoTo 0
        End With
    Next
End Function

Function Title1()
'一级标题
    Dim doc As Document, i As Paragraph

    Set doc = ActiveDocument

    With doc.Paragraphs(1).Range
        If .End <> doc.Content.End Then
            If Not (.Next(4, 1) Like "*[。：；，、！？…—.:;,!?]?" Or .Next(4, 1) Like "[一1][、.．]*" Or .Next(4, 1) Like "（[一1]）*" Or .Next(4, 1) Like "第[一1]*" Or .Next.Information(12)) Then .MoveEnd 4
        End If
        If .End <> doc.Content.End Then
            If Not (.Next(4, 1) Like "*[。：；，、！？…—.:;,!?]?" Or .Next(4, 1) Like "[一1][、.．]*" Or .Next(4, 1) Like "（[一1]）*" Or .Next(4, 1) Like "第[一1]*" Or .Next.Information(12)) Then .MoveEnd 4
        End If
        If .End <> doc.Content.End Then
            .Characters.Last.InsertParagraphBefore
        End If
        .InsertParagraphBefore
        .Style = wdStyleHeading1
        With .Font
            .NameFarEast = "方正小标宋_GBK"
            .NameAscii = "宋体"
            .Kerning = 0
            .Size = 22  '一级标题字体大小
        End With
        With .ParagraphFormat
            .SpaceBeforeAuto = False
            .SpaceAfterAuto = False
            .SpaceBefore = 0
            .SpaceAfter = 0
            .LineSpacingRule = wdLineSpaceExactly
            .SpaceBefore = 35  ' 上行距35磅
            .SpaceAfter = 15   ' 下行距15磅
            .Alignment = wdAlignParagraphCenter
            .AutoAdjustRightIndent = False
            .DisableLineHeightGrid = True
        End With

        '称呼
        If .End <> doc.Content.End Then
            With .Next(4, 1)
                If Not .Information(12) Then
                    If .Text Like "*[：:]?" Then
                        If .ComputeStatistics(1) < 3 Then
                            .Characters.Last.Previous.Text = "："
                            With .Find
                                .Execute "(", , , 0, , , , , , "（", 2
                                .Execute ")", , , 0, , , , , , "）", 2
                            End With
                            .Font.Color = wdColorViolet
                            With .ParagraphFormat
                                .CharacterUnitFirstLineIndent = 0
                                .FirstLineIndent = CentimetersToPoints(0)
                            End With
                        End If
                    End If
                End If
            End With
        End If

        '空格
        With .Find
            .Execute "(", , , 0, , , , , , "（", 2
            .Execute ")", , , 0, , , , , , "）", 2
            .Execute "[ 　^s^t]", , , 1, , , , , , "", 2
        End With

        '（草稿）
        With .Paragraphs.Last.Previous.Range
            If .Text Like "（*）?" Then
                With .Font
                    .NameFarEast = "方正楷体_GBK"
                    .NameAscii = "宋体"
                    .Size = 18
                    .Color = wdColorTeal
                End With
                .Paragraphs.IncreaseSpacing
                If Len(.Text) = 6 Then
                    .Characters(2).InsertAfter Text:=" "
                    .Characters(4).InsertAfter Text:=" "
                ElseIf Len(.Text) = 5 Then
                    .Characters(2).InsertAfter Text:=" "
                End If
                .Next.ParagraphFormat.Space1
            End If

            '加空
            If Not .Text Like "*）*" Then
                If .Text Like "???" Then
                    .Characters(1).InsertAfter Text:="    "
                ElseIf .Text Like "????" Then
                    If .Text Like "协议书?" Then .Font.Size = 26
                    .Characters(1).InsertAfter Text:="   "
                    .Characters(5).InsertAfter Text:="   "
                ElseIf .Text Like "?????" Then
                    .Characters(1).InsertAfter Text:="  "
                    .Characters(4).InsertAfter Text:="  "
                    .Characters(7).InsertAfter Text:="  "
                ElseIf .Text Like "??????" Then
                    .Characters(1).InsertAfter Text:="  "
                    .Characters(4).InsertAfter Text:="  "
                    .Characters(7).InsertAfter Text:="  "
                    .Characters(10).InsertAfter Text:="  "
                ElseIf .Text Like "???????" Then
                    .Characters(1).InsertAfter Text:=" " & ChrW(160)
                    .Characters(4).InsertAfter Text:=" " & ChrW(160)
                    .Characters(7).InsertAfter Text:=" " & ChrW(160)
                    .Characters(10).InsertAfter Text:=" " & ChrW(160)
                    .Characters(13).InsertAfter Text:=" " & ChrW(160)
                End If
            End If
        End With

        For Each i In .Paragraphs
            With i.Range
                If .Text Like "[!（]*[）”〉》]?" Then .InsertBefore Text:=" "
                If .Text Like "[“（《〈]*[!）]?" Then .ParagraphFormat.CharacterUnitLeftIndent = -0.5
            End With
        Next

        '表格
        If .Next.Information(12) Then
            .Characters.First.Delete
            .Characters.Last.Delete
            .ParagraphFormat.Space15
        End If
    End With
End Function

Function Inscribe()
'落款
    Dim doc As Document, r As Range, arr, TextSize&, Base!, lenUnit&, k&

    Set doc = ActiveDocument

    '2022-12-09
    Set r = doc.Content
    With r.Find
        .ClearFormatting
        .Text = "^13[0-9]{4}?[0-9]{1,2}?[0-9]{1,2}[^13^12]"
        .Forward = True
        .MatchWildcards = True
        Do While .Execute
            With .Parent
                .MoveStart
                .Characters(5).Text = "年"
                .Characters.Last.InsertBefore Text:="日"
                If .Characters(7) Like "[0-9]" Then
                    .Characters(8).Text = "月"
                Else
                    .Characters(7).Text = "月"
                End If
                .Start = .End
            End With
        Loop
    End With

    Set r = doc.Content
    With r.Find
        .ClearFormatting
        .Text = "^13[0-9]{4}年[0-9]{1,2}月[0-9]{1,2}日[^13^12]"
        .Forward = True
        .MatchWildcards = True
        Do While .Execute
            With .Parent
                .MoveStart
                If .Text Like "*0?月*" Then .Characters(6).Delete
                If .Text Like "*0?日*" Then .Characters.Last.Previous.Previous.Previous.Delete
                If Not .Font.Size = 16 Then k = 1: Exit Do
                .Start = .End
            End With
        Loop
        If k = 0 Then Exit Function
    End With

    'date
    With r
        .Font.Color = wdColorPink
        .Font.NameFarEast = "方正仿宋_GBK"
        .Font.NameAscii = "宋体"
        TextSize = .Font.Size

        With .ParagraphFormat
            .Alignment = wdAlignParagraphRight
            .CharacterUnitRightIndent = 4  '右空四字
            .LineSpacingRule = wdLineSpaceExactly
            .LineSpacing = 29  '固定值29磅
            .SpaceBefore = 58  '空两行（29磅*2）
        End With

        If TextSize = 16 Then
            If .Text Like "*年?月?日?" Then
                Base = 18.22
            ElseIf .Text Like "*年?月??日?" Or .Text Like "*年??月?日?" Then
                Base = 17.97
            Else
                Base = 17.72
            End If
        Else
            If .Text Like "*年?月?日?" Then
                Base = 20.7
            ElseIf .Text Like "*年?月??日?" Or .Text Like "*年??月?日?" Then
                Base = 20.45
            Else
                Base = 20.2
            End If
        End If

        'unit
        With .Previous(4, 1)
            If .Text Like "*[!。：；，、！？…—.:;,!?]?" Then
                .Font.Color = wdColorRed
                .Font.NameFarEast = "方正仿宋_GBK"
                .Font.NameAscii = "宋体"
                .InsertBefore Text:=vbCr & vbCr  '空两行
                .SetRange Start:=.Paragraphs.Last.Range.Start, End:=.Paragraphs.Last.Range.End
                lenUnit = Len(.Text) - 1

                If lenUnit = 9 Then
                    .Font.Spacing = 1
                ElseIf lenUnit = 8 Then
                    .Font.Spacing = 2
                ElseIf lenUnit = 2 Then
                    .Characters(1).InsertAfter Text:="  "
                ElseIf lenUnit = 3 Then
                    .Characters(1).InsertAfter Text:=" "
                    .Characters(3).InsertAfter Text:=" "
                ElseIf lenUnit = 4 Then
                    .Font.Spacing = 3
                ElseIf lenUnit = 5 Then
                    .Font.Spacing = 1
                End If

                If TextSize = 16 Then
                    arr = Array(1.2, 1.6, 6.5, 4.15, 2.7, 3.35, 7.45, 6.45, 5, 5.5, 6.1, 6.6, 7.2, 7.7, 8.25, 9.25, 10.25, 11.25, 12.25, 13.25, 14.25, 15.25, 16.25, 17.25)
                Else
                    arr = Array(1.2, 1.7, 7.85, 4.85, 2.75, 3.35, 8.55, 7.15, 5.15, 5.65, 6.25, 6.75, 7.15, 7.75, 8.35, 8.4, 9.3, 10.45, 11.35, 12.45, 13.45, 14.45, 15.45, 16.45)
                End If
                .ParagraphFormat.CharacterUnitFirstLineIndent = Base - arr(lenUnit - 2)
                .ParagraphFormat.SpaceBefore = 58  '空两行
                .ParagraphFormat.LineSpacingRule = wdLineSpaceExactly
                .ParagraphFormat.LineSpacing = 29  '固定值29磅

                'date-indent
                With .Next(4, 1).ParagraphFormat
                    If lenUnit < 17 Then
                    ElseIf lenUnit = 17 Then
                        .CharacterUnitRightIndent = 6.5
                    ElseIf lenUnit = 18 Then
                        .CharacterUnitRightIndent = 7
                    ElseIf lenUnit = 19 Then
                        .CharacterUnitRightIndent = 7.85
                    ElseIf lenUnit = 20 Then
                        .CharacterUnitRightIndent = 8.52
                    ElseIf lenUnit = 21 Then
                        .CharacterUnitRightIndent = 9.2
                    ElseIf lenUnit = 22 Then
                        .CharacterUnitRightIndent = 9.88
                    ElseIf lenUnit = 23 Then
                        .CharacterUnitRightIndent = 10.55
                    ElseIf lenUnit = 24 Then
                        .CharacterUnitRightIndent = 11.22
                    ElseIf lenUnit >= 25 Then
                        lenUnit = 25
                        .CharacterUnitRightIndent = 12
                    End If
                End With
            Else
                .InsertParagraphAfter
                Exit Function
            End If
        End With
    End With

    If doc.Content Like "*" & vbCr & "附*" = False Then Exit Function
'附件
    Dim DateRange As Range, myRange As Range, i As Paragraph, j&, n&, oBefore&, oAfter&, oTitle$
'前附件
    Set DateRange = r
    Set r = doc.Range(Start:=0, End:=DateRange.End)
    With r.Find
        .ClearFormatting
        .Text = "^13附件*^13"
        .Forward = True
        .MatchWildcards = True
        .Execute
        If .Found = True Then
            With .Parent
                .MoveStart
                Do
                    .MoveEnd 4
                Loop Until .Text Like "*" & vbCr & vbCr
                .MoveEnd 1, -1
                .InsertParagraphBefore
                .MoveStart
                oBefore = 1
                Set myRange = r
            End With
        End If
    End With
'后附件
sc:
    Set r = doc.Range(Start:=DateRange.End - 1, End:=doc.Content.End)
    With r.Find
        .ClearFormatting
        .Text = "[^13^12]附件*^13"
        .Forward = True
        .MatchWildcards = True
        Do While .Execute
            With .Parent
                If Asc(.Text) = 13 Then
                    .Characters(1).InsertAfter Text:=Chr(12)
                ElseIf Asc(.Text) = 12 Then
                    .MoveStart 1, -1
                End If
                .MoveStart 1, 2

                'special
                Do While .Next(4, 1) Like "#．*" & vbCr Or .Next(4, 1) Like "##．*" & vbCr
                    .MoveEnd 4
                    If .End = doc.Content.End Then
                        oTitle = .Text
                        .Delete
                        .Previous.Delete
                        .Delete
                        oAfter = 1
                        GoTo sk
                    End If
                Loop

                .MoveEnd 1, -1
                n = n + 1
                .Text = "附件" & n & "："

                With .Font
                    .NameFarEast = "方正黑体_GBK"
                    .NameAscii = "宋体"
                    .Bold = False
                    .Color = wdColorRed
                End With
                With .ParagraphFormat
                    .CharacterUnitFirstLineIndent = 0
                    .FirstLineIndent = CentimetersToPoints(0)
                    .LineSpacingRule = wdLineSpaceExactly
                    .LineSpacing = 29
                End With

                'title
                With .Next(4, 1)
                    If Not .Information(12) Then
                        If Not (.Next.Information(12)) Then
                            If Not (.Next(4, 1) Like "*[。：:_]*" Or .Next(4, 1) Like "[一1][、.．]*" Or .Next(4, 1) Like "（[一1]）*" Or .Next(4, 1) Like "第一*") Then
                                .MoveEnd 4
                                .Paragraphs(1).Range.Characters.Last.Delete
                            End If
                        End If
                        If Not (.Next.Information(12)) Then
                            If Not (.Next(4, 1) Like "*[。：:_]*" Or .Next(4, 1) Like "[一1][、.．]*" Or .Next(4, 1) Like "（[一1]）*" Or .Next(4, 1) Like "第一*") Then
                                .MoveEnd 4
                                .Paragraphs(1).Range.Characters.Last.Delete
                            End If
                        End If
                    Else
                        .Next.Next.Select
                        Selection.SplitTable
                        Selection.Previous.Tables(1).Rows.ConvertToText Separator:=wdSeparateByParagraphs, NestedTables:=True
                        .Next(4, 1).Delete
                        .Expand 4
                    End If

                    .Find.Execute "[ 　^s^t]", , , 1, , , , , , "", 2

                    oTitle = oTitle & .Text
                    With .Font
                        .NameFarEast = "方正宋体_GBK"
                        .NameAscii = "宋体"
                        .Size = 20
                        .Bold = False
                        .Color = wdColorAutomatic
                    End With
                    With .ParagraphFormat
                        .CharacterUnitFirstLineIndent = 0
                        .FirstLineIndent = CentimetersToPoints(0)
                        .Alignment = wdAlignParagraphCenter
                        .LineSpacingRule = wdLineSpaceExactly
                        .LineSpacing = 29
                    End With
                    If .Sections(1).PageSetup.Orientation = wdOrientPortrait Then
                        .InsertParagraphBefore
                        .Characters.Last.InsertBefore Text:=vbCr
                        .ParagraphFormat.LineSpacing = LinesToPoints(1.25)
                    End If
                    .Paragraphs.Last.Range.ParagraphFormat.Space15
                    If .Text Like "*[）”〉》]?" Then .InsertBefore Text:=" "
                    If .Text Like "[“（《〈]*[!）]?" Then .ParagraphFormat.CharacterUnitLeftIndent = -0.5
                End With
                oAfter = 1
                .Start = .End
            End With
        Loop
    End With

    'logo miss
    With r
        If oAfter = 0 And Len(.Text) > 1 Then
            If .Text Like vbCr & Chr(12) & "*" Then
                .Characters(2).InsertAfter Text:="附件：" & vbCr
            ElseIf .Text Like vbCr & "*" Then
                .Characters(1).InsertAfter Text:="附件：" & vbCr
            ElseIf .Characters(2).Information(12) Then
                .Characters(2).Select
                With Selection
                    .SplitTable
                    .TypeText Text:="附件："
                    With .Paragraphs(1).Range
                        .Font.Size = 16
                        .Font.NameFarEast = "方正仿宋_GBK"
                        .Font.NameAscii = "宋体"
                        With .ParagraphFormat
                            .LineSpacingRule = wdLineSpaceExactly
                            .LineSpacing = 29
                            .AutoAdjustRightIndent = False
                            .DisableLineHeightGrid = True
                        End With
                    End With
                End With
            End If
            GoTo sc
        End If
        If n = 1 Then .Previous.Previous.Delete
    End With
    If oBefore = 0 And oAfter = 0 Then Exit Function
'讨论
    If oBefore = 1 Then
        If oAfter = 1 Then
            With myRange
                If .Text Like "附件[：:]" & vbCr & "*" Then .Paragraphs(1).Range.Delete
                If .Paragraphs.Count = n Then
                    .Text = oTitle
                Else
                    If MsgBox("<前附件> " & .Paragraphs.Count & " 个：" & vbCr & .Text & vbCr _
                        & "<后附件> " & n & " 个：" & vbCr & oTitle & vbCr & "* 落款前后附件个数不一致！请选择：" & vbCr _
                        & "[是(Y)] 以<前附件>为准！     [否(N)] 以<后附件>为准！", 4 + 16) = vbNo Then .Text = oTitle
                End If
            End With
        End If
    Else
sk:
        If oAfter = 1 Then
            With DateRange
                .MoveStart 4, -4
                .InsertBefore Text:=vbCr & oTitle
                .MoveStart
                .MoveEnd 4, -5
            End With
            Set myRange = DateRange
        End If
    End If
'缩进
    With myRange
        With .Font
            .Color = wdColorBrown
            .NameFarEast = "方正仿宋_GBK"
            .NameAscii = "宋体"
            .Bold = False
        End With
        With .ParagraphFormat
            .SpaceBefore = 0
            .SpaceAfter = 0
            .LineSpacingRule = wdLineSpaceExactly
            .LineSpacing = 29
        End With

        For Each i In .Paragraphs
            With i.Range
                If .Text Like "附*" Then .Characters(1).Delete
                If .Text Like "件*" Then .Characters(1).Delete
                If .Text Like "表*" Then .Characters(1).Delete
                If .Text Like "[一二三四五六七八九十]*" Then .Characters(1).Delete
                If .Text Like "#[：:.．、，]*" Or .Text Like "##[：:.．、，]*" Then .Characters(1).Delete
                If .Text Like "#[：:.．、，]*" Or .Text Like "##[：:.．、，]*" Then .Characters(1).Delete
                If .Text Like "[：:.．、，]*" Then .Characters(1).Delete
                If .Text Like "《*" Then .Characters(1).Delete
                If .Text Like "*》?" Then .Characters.Last.Previous.Delete

                If .Text Like "#[：:.．、，]*" Or .Text Like "##[：:.．、，]*" Then .Characters(1).Delete
                If .Text Like "#[：:.．、，]*" Or .Text Like "##[：:.．、，]*" Then .Characters(1).Delete
                If .Text Like "[：:.．、，]*" Then .Characters(1).Delete
            End With
        Next

        If oBefore = 1 And oAfter = 0 Then n = myRange.Paragraphs.Count

        If n = 1 Then
            .InsertBefore Text:=vbTab
        Else
            For Each i In .Paragraphs
                j = j + 1
                i.Range.InsertBefore Text:=j & "．" & vbTab
            Next
        End If

        With .ParagraphFormat
            .CharacterUnitLeftIndent = 7.68
            .CharacterUnitFirstLineIndent = -1.56
            .LineSpacingRule = wdLineSpaceExactly
            .LineSpacing = 29
        End With

        .InsertBefore Text:="附件："

        With .Paragraphs(1).Range.ParagraphFormat
            .CharacterUnitLeftIndent = 3.05
            .CharacterUnitFirstLineIndent = -4.62
            .LineSpacingRule = wdLineSpaceExactly
            .LineSpacing = 29
        End With

        If n = 1 Then .ParagraphFormat.CharacterUnitFirstLineIndent = -3.1
    End With
End Function

Function PageNumGW()
'公文页码
    Dim Rng As Range
    With ActiveDocument.Sections(1)
        '清除页眉内容，防止干扰
        .Headers(wdHeaderFooterPrimary).Range.Delete

        With .Footers(wdHeaderFooterPrimary)
            .Range.Delete
            If .Parent.Parent.ComputeStatistics(wdStatisticPages) > 1 Then
                '使用Selection方法来确保正确插入
                .Range.Select
                Selection.TypeText "— "
                Selection.Fields.Add Range:=Selection.Range, Type:=wdFieldPage
                Selection.TypeText " —"

                '设置整个页脚的格式
                With .Range
                    .Font.NameAscii = "宋体"
                    .Font.Size = 12  '4号字体
                    .ParagraphFormat.Alignment = wdAlignParagraphLeft
                    .ParagraphFormat.SpaceBefore = 0
                    .ParagraphFormat.SpaceAfter = 0
                End With

                '更新字段
                .Range.Fields.Update
            End If
        End With
    End With
End Function

Function Common()
    NumPages  '页数
    StyleReset   '公文样式
    Selection.HomeKey Unit:=wdStory
    AutoColor  '字体恢复为默认颜色
End Function

Function NumPages()
'页数
    Dim p&
    p = ActiveDocument.ComputeStatistics(wdStatisticPages)
    With ActiveWindow.ActivePane.View.Zoom
        If p < 99 Then
            If .PageColumns = p Then .PageColumns = 3 Else .PageColumns = p
        Else
            If .PageColumns = 15 Then .PageColumns = 3 Else .PageColumns = 15
        End If
        .PageRows = 1
    End With
End Function

Function StyleReset()
'设置公文样式
    With ActiveDocument
        With .Styles(wdStyleNormal).Font  '正文
            .NameFarEast = "方正仿宋_GBK"
            .NameAscii = "宋体"
            .Size = 16  '3号字体
        End With
        With .Styles(wdStyleNormal).ParagraphFormat
            .LineSpacingRule = wdLineSpaceExactly
            .LineSpacing = 29  '固定值29磅
        End With

        With .Styles(wdStyleHeading1).Font  '一级标题
            .NameFarEast = "方正黑体_GBK"
            .NameAscii = "宋体"
            .Size = 26
        End With
        With .Styles(wdStyleHeading1).ParagraphFormat
            .LineSpacingRule = wdLineSpaceExactly
            .LineSpacing = 15  '标题行距15磅
        End With

        With .Styles(wdStyleHeading2).Font   '二级标题
            .NameFarEast = "方正楷体_GBK"
            .NameAscii = "宋体"
            .Size = 16
        End With
        With .Styles(wdStyleHeading2).ParagraphFormat
            .LineSpacingRule = wdLineSpaceExactly
            .LineSpacing = 29
        End With

        With .Styles(wdStyleHeading3).Font   '三级标题
            .NameFarEast = "方正仿宋_GBK"
            .NameAscii = "宋体"
            .Size = 16
        End With
        With .Styles(wdStyleHeading3).ParagraphFormat
            .LineSpacingRule = wdLineSpaceExactly
            .LineSpacing = 29
        End With

        With .Styles(wdStyleHeading4).Font   '四级标题
            .NameFarEast = "方正仿宋_GBK"
            .NameAscii = "宋体"
            .Size = 16
        End With
        With .Styles(wdStyleHeading4).ParagraphFormat
            .LineSpacingRule = wdLineSpaceExactly
            .LineSpacing = 29
        End With

        With .Styles(wdStyleHeading5).Font  '五级标题
            .NameFarEast = "方正仿宋_GBK"
            .NameAscii = "宋体"
            .Size = 16
        End With
        With .Styles(wdStyleHeading5).ParagraphFormat
            .LineSpacingRule = wdLineSpaceExactly
            .LineSpacing = 29
        End With
    End With
End Function

Function AutoColor()
    ActiveDocument.Content.Font.Color = wdColorAutomatic
End Function